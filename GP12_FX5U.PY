
import tkinter as tk
from pymodbus.client import ModbusTcpClient
from datetime import datetime
import logging
import threading
import time
import subprocess
import platform
import socket
import pyodbc  # Add pyodbc import for SQL Server connection

logging.basicConfig()
log = logging.getLogger()
log.setLevel(logging.ERROR)  # reduce pymodbus logs in GUI

PLC_IP = '*************'
PLC_PORT = 502
UNIT_ID = 1

# SQL Server connection configuration
SQL_SERVER = 'MSV\\SQLEXPRESS'
SQL_DATABASE = 'GP12'
SQL_DRIVER = '{ODBC Driver 17 for SQL Server}'

start_address = 5500
register_count = 42  # Read 40 registers for up to 40 characters

class PLCReaderApp:
    def __init__(self, root):
        self.root = root
        self.is_connected = False  # Move flag initialization to start
        self.root.title("FX5U PLC Data Reader")

        # Date and time labels at the top
        self.datetime_frame = tk.Frame(root)
        self.datetime_frame.pack(fill="x", pady=10)
        self.date_label = tk.Label(self.datetime_frame, text="", font=("Arial", 20, "bold"), anchor="w")
        self.date_label.pack(side="left", padx=10)
        self.time_label = tk.Label(self.datetime_frame, text="", font=("Arial", 20, "bold"), anchor="e")
        self.time_label.pack(side="right", padx=10)

        # Title label with bigger and bold font
        tk.Label(root, text="GP12 INSPECTION STANDARD BARCODE TRACEABILITY SYSTEMS",
                 font=("Arial", 30, "bold"), fg="blue").pack(pady=10)

        # PLC connection status with border
        self.status_frame = tk.Frame(root, highlightbackground="black", highlightthickness=1)
        self.status_frame.pack(anchor="w", padx=10, pady=10)
        self.status_label = tk.Label(self.status_frame, text="PLC Status: Disconnected",
                                     font=("Arial", 16, "bold"), fg="red", anchor="w")
        self.status_label.pack(side="left", padx=10)
        self.connection_info_label = tk.Label(self.status_frame, text="",
                                              font=("Arial", 16, "bold"), fg="blue", anchor="w")

        # Data Display Area with border for better visibility
        self.data_display_frame = tk.Frame(root, highlightbackground="black", highlightthickness=1)
        self.data_display_frame.pack(fill="x", padx=10, pady=(5, 10))

        tk.Label(self.data_display_frame, text="Barcode Data:",
                 font=("Arial", 20, "bold"), anchor="w").pack(side="left", padx=(10, 5))

        # Enhanced data display with better visibility and more space
        self.data_label = tk.Label(self.data_display_frame, text="---",
                                   font=("Arial", 25, "bold"), anchor="w", fg="purple", bg="white",
                                   wraplength=800, justify="left", padx=5, pady=5, height=2)
        self.data_label.pack(side="left", fill="x", expand=True, padx=(0, 10), pady=5)

        # Add a debug frame to show raw register values
        self.debug_frame = tk.Frame(root)
        self.debug_frame.pack(fill="x", padx=10, pady=5)

        self.debug_button = tk.Button(self.debug_frame, text="Show Raw Registers",
                                     command=self.toggle_debug_window)
        self.debug_button.pack(side="left", padx=10)

        # Add SQL Save button
        self.save_button = tk.Button(self.debug_frame, text="Save to Database",
                                    command=self.save_to_database, bg="#4CAF50", fg="white")
        self.save_button.pack(side="left", padx=10)

        # Add Manual Entry button
        self.manual_button = tk.Button(self.debug_frame, text="Manual Entry",
                                      command=self.show_manual_entry, bg="#2196F3", fg="white")
        self.manual_button.pack(side="left", padx=10)

        # Add View Database button
        self.view_db_button = tk.Button(self.debug_frame, text="Refresh Database View",
                                       command=self.refresh_database_display, bg="#FF9800", fg="white")
        self.view_db_button.pack(side="left", padx=10)

        # Add status label for database operations
        self.db_status_label = tk.Label(self.debug_frame, text="", font=("Arial", 10))
        self.db_status_label.pack(side="left", padx=10)

        self.debug_window = None
        self.manual_entry_window = None

        # Create database records display section in main window
        self.create_database_display_section()

        # Add footer with running message
        self.footer_frame = tk.Frame(root, bg="#333333", height=30)
        self.footer_frame.pack(side="bottom", fill="x")

        self.footer_text = "Software developed by Python | GP12 INSPECTION STANDARD BARCODE TRACEABILITY SYSTEMS | " * 3
        self.footer_label = tk.Label(self.footer_frame, text=self.footer_text, fg="white", bg="#333333",
                                    font=("Arial", 10))
        self.footer_label.pack(pady=5)

        # Start the running message animation
        self.footer_position = 0
        self.animate_footer()

        # Attempt to connect to PLC
        self.socket_timeout = 0.5  # Add socket timeout setting
        self.client = ModbusTcpClient(PLC_IP, port=PLC_PORT, timeout=self.socket_timeout)
        self.client.unit_id = UNIT_ID
        self.connection = self.client.connect()

        if not self.connection:
            self.connection_info_label.config(text="Failed to connect to PLC", fg="red")
            self.connection_info_label.pack(side="left", padx=10) # Pack it even on failure
            self.update_status_label(connected=False) # Ensure status label is also updated
            return
        self.connection_info_label.config(text=f"at {PLC_IP}") # Shorter message
        self.connection_info_label.pack(side="left", padx=(0,10)) # Pack next to status
        self.update_status_label(connected=self.connection) # Update status label after connection attempt
        # Start reading in a thread to keep GUI responsive
        self.running = True
        threading.Thread(target=self.read_loop, daemon=True).start()

        # Start updating date and time
        self.update_datetime()

        # When window is closed, stop loop & close client
        self.root.protocol("WM_DELETE_WINDOW", self.on_close)

        self.last_refresh = time.time()  # Initialize last refresh time
        self.last_data = ""  # Track the last data to detect changes
        self.current_barcode = ""  # Store the current barcode data

    def update_datetime(self):
        now = datetime.now()
        self.date_label.config(text=now.strftime("%Y-%m-%d"))
        self.time_label.config(text=now.strftime("%H:%M:%S"))
        self.root.after(1000, self.update_datetime)  # Schedule the next update

    def update_status_label(self, connected):
        if connected:
            self.status_label.config(text="PLC Status: Connected", fg="green")
        else:
            self.status_label.config(text="PLC Status: Disconnected", fg="red")

    def check_plc_connection(self):
        try:
            # Quick socket test first
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.socket_timeout)
            result = sock.connect_ex((PLC_IP, PLC_PORT))
            sock.close()

            if result != 0:
                self.client.close()
                return False

            if not self.client.connected:
                if not self.client.connect():
                    return False

            # Verify Modbus communication using keyword arguments
            test_result = self.client.read_holding_registers(address=start_address, count=1)
            if test_result is None or test_result.isError():
                self.client.close()
                return False
            return True

        except (socket.error, Exception) as e:
            print(f"Connection check error: {e}")
            self.client.close()
            return False

    def read_loop(self):
        while self.running:
            try:
                # Check connection status
                current_status = self.check_plc_connection()

                # Update status immediately when connection state changes
                if current_status != self.is_connected:
                    self.is_connected = current_status
                    self.update_status_label(connected=current_status)
                    if not current_status:
                        self.root.after(0, self.update_data_display, "---") # Clear data on disconnect
                        continue  # Skip further processing if disconnected

                # Read registers continuously if connected
                if self.is_connected:
                    # Read registers D5500-D5539 (40 registers)
                    result = self.client.read_holding_registers(address=start_address, count=register_count)
                    if result is None or result.isError():
                        print(f"Modbus read error: {result}")
                        raise Exception("Failed to read registers")

                    values = result.registers

                    # Check if we have any non-zero values
                    non_zero_values = [v for v in values if v != 0]
                    if not non_zero_values:
                        print("All register values are zero!")

                    # Convert register values to barcode string
                    barcode_data = self.convert_to_barcode(values)

                    # Always update the display with the latest data
                    self.root.after(0, self.update_data_display, barcode_data)

                # Small sleep to prevent CPU overload but ensure continuous reading
                time.sleep(0.1)

            except Exception as e:
                # Handle disconnection or errors
                self.is_connected = False
                self.update_status_label(connected=False)
                self.root.after(0, self.update_data_display, "---")
                print(f"Read loop error: {e}")
                time.sleep(0.5)  # Wait a bit before retrying

    def convert_to_barcode(self, values):
        """Convert register values to a barcode string"""
        if not values:
            return "No barcode data available"

        # Print raw values for debugging
        print("\nRaw register values for barcode conversion:")
        for i in range(min(21, len(values))):  # Increased to 21 to include D5520
            val = values[i]
            char_val = chr(val) if 32 <= val <= 126 else '.'
            print(f"Index {i}: {val} (ASCII: {char_val})")

        # The correct format should be: 010:1104AAA07121N:DM216A:240525:02:0014
        # But we're getting: 10:01140AA0A1712:NMD12A62:0425:5200:104
        # This suggests the bytes are swapped or in wrong order

        # Extract ASCII characters from high and low bytes in CORRECT order
        barcode_string = ""
        for val in values:
            if val == 0:
                continue

            # Extract low byte FIRST, then high byte (reverse order)
            low_byte = val & 0xFF
            high_byte = (val >> 8) & 0xFF

            # Add printable characters in correct order
            if 32 <= low_byte <= 126:
                barcode_string += chr(low_byte)
            if 32 <= high_byte <= 126:
                barcode_string += chr(high_byte)

        # Clean up the barcode string
        # Remove any spaces and control characters
        barcode_string = ''.join(barcode_string.split())
        barcode_string = barcode_string.rstrip(' \t\r\n\0')

        # If the barcode is empty after processing, return a message
        if not barcode_string or barcode_string.isspace():
            print("No valid characters found in barcode data")
            return "No barcode data detected"

        # Check if the barcode matches the expected format
        # Expected format: 010:1104AAA07121N:DM216A:240525:02:0014
        expected_format = r'\d+:\d+[A-Z0-9]+:[A-Z0-9]+:\d+:\d+:\d+'
        import re
        if re.search(expected_format, barcode_string):
            print(f"Found valid barcode format: {barcode_string}")
        else:
            print(f"Warning: Barcode may not match expected format: {barcode_string}")

        # Log the processed barcode string
        print(f"Processed barcode string: '{barcode_string}'")

        return barcode_string

    def on_close(self):
        self.running = False
        if self.connection:
            self.client.close()
        self.root.destroy()

    def update_data_display(self, data_text):
        """Update the data display with the latest PLC data"""
        # Store the current barcode data
        self.current_barcode = data_text

        # Check if the data matches our expected format
        import re
        # Updated pattern to match the actual format: 10:01140AA0A1712:NMD12A62:0425:5200:1014
        expected_format = r'\d+:\d+[A-Z0-9]+:[A-Z0-9]+:\d+:\d+:\d+'

        if re.search(expected_format, data_text):
            # Valid barcode format - highlight in green
            self.data_label.config(text=data_text, bg="#e0ffe0", fg="black")  # Light green background
        elif data_text == "---" or data_text == "No barcode data detected":
            # No data - use default style
            self.data_label.config(text=data_text, bg="white", fg="red")
        else:
            # Data present but not in expected format - highlight in yellow
            self.data_label.config(text=data_text, bg="#ffffd0", fg="black")  # Light yellow background

    def toggle_debug_window(self):
        """Toggle the debug window to show raw register values"""
        if self.debug_window is None or not self.debug_window.winfo_exists():
            self.debug_window = tk.Toplevel(self.root)
            self.debug_window.title("Raw Register Values")
            self.debug_window.geometry("600x400")

            # Create a text widget to display register values
            self.debug_text = tk.Text(self.debug_window, font=("Courier New", 12))
            self.debug_text.pack(fill="both", expand=True, padx=10, pady=10)

            # Add a refresh button
            refresh_button = tk.Button(self.debug_window, text="Refresh",
                                      command=self.update_debug_window)
            refresh_button.pack(pady=10)

            # Initial update
            self.update_debug_window()
        else:
            self.debug_window.destroy()
            self.debug_window = None

    def update_debug_window(self):
        """Update the debug window with current register values"""
        if self.debug_window is None or not self.debug_window.winfo_exists():
            return

        if not self.is_connected:
            self.debug_text.delete(1.0, tk.END)
            self.debug_text.insert(tk.END, "PLC not connected")
            return

        try:
            # Read registers D5500-D5539
            result = self.client.read_holding_registers(address=start_address, count=register_count)
            if result is None or result.isError():
                self.debug_text.delete(1.0, tk.END)
                self.debug_text.insert(tk.END, "Failed to read registers")
                return

            values = result.registers

            # Clear the text widget
            self.debug_text.delete(1.0, tk.END)

            # Add header
            self.debug_text.insert(tk.END, "Register  Value  Hex      Low Byte   High Byte\n")
            self.debug_text.insert(tk.END, "-----------------------------------------------\n")

            # Add register values with byte breakdown
            has_data = False
            for i, val in enumerate(values):
                reg_addr = start_address + i
                hex_val = f"0x{val:04X}"
                low_byte = val & 0xFF
                high_byte = (val >> 8) & 0xFF
                low_char = chr(low_byte) if 32 <= low_byte <= 126 else '.'
                high_char = chr(high_byte) if 32 <= high_byte <= 126 else '.'

                line = f"D{reg_addr:<6}  {val:<5}  {hex_val:<8}  {low_byte:3} ({low_char})  {high_byte:3} ({high_char})\n"
                self.debug_text.insert(tk.END, line)
                if val != 0:
                    has_data = True

            if not has_data:
                self.debug_text.insert(tk.END, "\nWARNING: All registers contain zero values!\n")
                self.debug_text.insert(tk.END, "Check PLC configuration and data writing.\n")

            # Try different decoding methods
            self.debug_text.insert(tk.END, "\n--- Decoding Attempts ---\n")

            # Method 1: Extract ASCII from high/low bytes (original order)
            ascii_from_bytes_original = ""
            for val in values:
                if val == 0:
                    continue
                high_byte = (val >> 8) & 0xFF
                low_byte = val & 0xFF
                if 32 <= high_byte <= 126:
                    ascii_from_bytes_original += chr(high_byte)
                if 32 <= low_byte <= 126:
                    ascii_from_bytes_original += chr(low_byte)

            # Method 2: Extract ASCII from low/high bytes (reversed order)
            ascii_from_bytes_reversed = ""
            for val in values:
                if val == 0:
                    continue
                low_byte = val & 0xFF
                high_byte = (val >> 8) & 0xFF
                if 32 <= low_byte <= 126:
                    ascii_from_bytes_reversed += chr(low_byte)
                if 32 <= high_byte <= 126:
                    ascii_from_bytes_reversed += chr(high_byte)

            # Clean up the strings by removing spaces
            cleaned_original = ''.join(ascii_from_bytes_original.split())
            cleaned_reversed = ''.join(ascii_from_bytes_reversed.split())

            self.debug_text.insert(tk.END, f"Original byte order (High-Low): {cleaned_original}\n")
            self.debug_text.insert(tk.END, f"Reversed byte order (Low-High): {cleaned_reversed}\n\n")

            # Add the expected format for reference
            self.debug_text.insert(tk.END, "\nExpected Format Example:\n")
            self.debug_text.insert(tk.END, "010:1104AAA07121N:DM216A:240525:02:0014\n")

            # Check which format matches the expected pattern
            import re
            expected_format = r'\d+:\d+[A-Z0-9]+:[A-Z0-9]+:\d+:\d+:\d+'

            if re.search(expected_format, cleaned_original):
                self.debug_text.insert(tk.END, "\nOriginal byte order matches expected format!\n")

            if re.search(expected_format, cleaned_reversed):
                self.debug_text.insert(tk.END, "\nReversed byte order matches expected format!\n")

        except Exception as e:
            self.debug_text.delete(1.0, tk.END)
            self.debug_text.insert(tk.END, f"Error: {e}")

    def save_to_database(self):
        """Save the current barcode data to SQL Server database"""
        # Get the current barcode data directly from the label
        barcode_data = self.data_label.cget("text")

        print(f"Attempting to save barcode data: '{barcode_data}'")

        # Don't save if no valid data
        if barcode_data == "---" or barcode_data == "No barcode data detected" or not barcode_data:
            self.show_db_status("No valid data to save", "red")
            return

        # Check if the data matches our expected format
        import re
        expected_format = r'\d+:\d+[A-Z0-9]+:[A-Z0-9]+:\d+:\d+:\d+'
        if not re.search(expected_format, barcode_data):
            print(f"Warning: Barcode format may not be valid: '{barcode_data}'")
            # Continue anyway, but log the warning

        try:
            # Parse the barcode data to extract model information
            # Expected format: 010:1104AAA07121N:DM216A:240525:02:0014
            parts = barcode_data.split(':')
            if len(parts) < 3:
                self.show_db_status(f"Invalid barcode format: {barcode_data}", "red")
                return

            # Extract model name from the barcode (3rd segment)
            model_name = parts[2] if len(parts) > 2 else "UNKNOWN"

            # Get current date and time
            current_datetime = datetime.now()

            # Connect to SQL Server
            conn = None
            try:
                conn = pyodbc.connect(
                    f'DRIVER={SQL_DRIVER};SERVER={SQL_SERVER};DATABASE={SQL_DATABASE};'
                    f'Trusted_Connection=yes;'
                )

                cursor = conn.cursor()

                # Modified insert query - remove S_NO column since it's an identity column
                # Use correct column name: [DATE TIME] (with space, not "AND")
                insert_query = '''
                INSERT INTO dbo.parameter ([DATE TIME], MODEL_NAME, BARCODE_DATA, BARCODE_SIZE, REMARKES)
                VALUES (?, ?, ?, ?, ?)
                '''

                # Execute the query without S_NO
                cursor.execute(insert_query, (
                    current_datetime,
                    model_name,
                    barcode_data,
                    "Standard",  # Default barcode size
                    "Automatically saved from PLC Reader"
                ))

                # Get the identity value that was generated
                cursor.execute("SELECT @@IDENTITY")
                identity_value = cursor.fetchone()[0]

                # Commit the transaction
                conn.commit()

                # Show success message with the generated identity
                self.show_db_status(f"Data saved successfully! ID: {identity_value}", "green")

                # Refresh the database display in main window
                self.refresh_database_display()

            except Exception as e:
                if conn:
                    conn.rollback()
                self.show_db_status(f"Database error: {str(e)}", "red")
                print(f"Database error: {e}")

            finally:
                if conn:
                    conn.close()

        except Exception as e:
            self.show_db_status(f"Error: {str(e)}", "red")
            print(f"Error saving to database: {e}")

    def show_db_status(self, message, color="black"):
        """Show database operation status message"""
        self.db_status_label.config(text=message, fg=color)
        # Clear the message after 5 seconds
        self.root.after(5000, lambda: self.db_status_label.config(text=""))

    def show_manual_entry(self):
        """Show a window for manual barcode data entry"""
        if self.manual_entry_window is None or not self.manual_entry_window.winfo_exists():
            self.manual_entry_window = tk.Toplevel(self.root)
            self.manual_entry_window.title("Manual Barcode Entry")
            self.manual_entry_window.geometry("600x300")

            # Create a frame for the form
            form_frame = tk.Frame(self.manual_entry_window, padx=20, pady=20)
            form_frame.pack(fill="both", expand=True)

            # Barcode data entry
            tk.Label(form_frame, text="Barcode Data:", font=("Arial", 12)).grid(row=0, column=0, sticky="w", pady=5)
            self.manual_barcode_entry = tk.Entry(form_frame, font=("Arial", 12), width=40)
            self.manual_barcode_entry.grid(row=0, column=1, sticky="ew", pady=5)
            self.manual_barcode_entry.insert(0, "010:1104AAA07121N:DM216A:240525:02:0014")  # Example format

            # Model name entry
            tk.Label(form_frame, text="Model Name:", font=("Arial", 12)).grid(row=1, column=0, sticky="w", pady=5)
            self.manual_model_entry = tk.Entry(form_frame, font=("Arial", 12), width=40)
            self.manual_model_entry.grid(row=1, column=1, sticky="ew", pady=5)

            # Barcode size dropdown
            tk.Label(form_frame, text="Barcode Size:", font=("Arial", 12)).grid(row=2, column=0, sticky="w", pady=5)
            self.barcode_size_var = tk.StringVar(value="Standard")
            barcode_sizes = ["Small", "Standard", "Large"]
            self.barcode_size_dropdown = tk.OptionMenu(form_frame, self.barcode_size_var, *barcode_sizes)
            self.barcode_size_dropdown.config(font=("Arial", 12), width=10)
            self.barcode_size_dropdown.grid(row=2, column=1, sticky="w", pady=5)

            # Remarks entry
            tk.Label(form_frame, text="Remarks:", font=("Arial", 12)).grid(row=3, column=0, sticky="w", pady=5)
            self.remarks_entry = tk.Entry(form_frame, font=("Arial", 12), width=40)
            self.remarks_entry.grid(row=3, column=1, sticky="ew", pady=5)
            self.remarks_entry.insert(0, "Manual entry")

            # Buttons frame
            buttons_frame = tk.Frame(form_frame)
            buttons_frame.grid(row=4, column=0, columnspan=2, pady=20)

            # Save button
            save_button = tk.Button(buttons_frame, text="Save to Database",
                                   command=self.save_manual_entry, bg="#4CAF50", fg="white",
                                   font=("Arial", 12), padx=10, pady=5)
            save_button.pack(side="left", padx=10)

            # Cancel button
            cancel_button = tk.Button(buttons_frame, text="Cancel",
                                     command=self.manual_entry_window.destroy,
                                     font=("Arial", 12), padx=10, pady=5)
            cancel_button.pack(side="left", padx=10)

            # Status label
            self.manual_status_label = tk.Label(form_frame, text="", font=("Arial", 10))
            self.manual_status_label.grid(row=5, column=0, columnspan=2, pady=10)

            # Auto-fill model name from barcode when barcode changes
            self.manual_barcode_entry.bind("<KeyRelease>", self.auto_fill_model)
        else:
            self.manual_entry_window.lift()  # Bring window to front if already exists

    def auto_fill_model(self, event=None):
        """Auto-fill model name from barcode data"""
        barcode = self.manual_barcode_entry.get()
        parts = barcode.split(':')
        if len(parts) > 2:
            self.manual_model_entry.delete(0, tk.END)
            self.manual_model_entry.insert(0, parts[2])

    def save_manual_entry(self):
        """Save manually entered barcode data to database"""
        barcode_data = self.manual_barcode_entry.get().strip()
        model_name = self.manual_model_entry.get().strip()
        barcode_size = self.barcode_size_var.get()
        remarks = self.remarks_entry.get().strip()

        if not barcode_data:
            self.manual_status_label.config(text="Barcode data is required", fg="red")
            return

        if not model_name:
            # Try to extract from barcode
            parts = barcode_data.split(':')
            if len(parts) > 2:
                model_name = parts[2]
            else:
                model_name = "UNKNOWN"

        # Connect to SQL Server
        conn = None
        try:
            conn = pyodbc.connect(
                f'DRIVER={SQL_DRIVER};SERVER={SQL_SERVER};DATABASE={SQL_DATABASE};'
                f'Trusted_Connection=yes;'
            )

            cursor = conn.cursor()

            # Modified insert query - remove S_NO column since it's an identity column
            # Use correct column name: [DATE TIME] (with space, not "AND")
            insert_query = '''
            INSERT INTO dbo.parameter ([DATE TIME], MODEL_NAME, BARCODE_DATA, BARCODE_SIZE, REMARKES)
            VALUES (?, ?, ?, ?, ?)
            '''

            # Execute the query without S_NO
            cursor.execute(insert_query, (
                datetime.now(),
                model_name,
                barcode_data,
                barcode_size,
                remarks
            ))

            # Get the identity value that was generated
            cursor.execute("SELECT @@IDENTITY")
            identity_value = cursor.fetchone()[0]

            # Commit the transaction
            conn.commit()

            # Show success message with the generated identity
            self.manual_status_label.config(text=f"Data saved successfully! ID: {identity_value}", fg="green")

            # Update the main display with this barcode
            self.update_data_display(barcode_data)

            # Refresh the database display in main window
            self.refresh_database_display()

        except Exception as e:
            if conn:
                conn.rollback()
            self.manual_status_label.config(text=f"Database error: {str(e)}", fg="red")
            print(f"Database error: {e}")

        finally:
            if conn:
                conn.close()

    def animate_footer(self):
        """Animate the footer text to create a running message effect"""
        # Shift the text by one character
        self.footer_position = (self.footer_position + 1) % len(self.footer_text)
        shifted_text = self.footer_text[self.footer_position:] + self.footer_text[:self.footer_position]
        self.footer_label.config(text=shifted_text)

        # Schedule the next animation frame
        self.root.after(100, self.animate_footer)  # Update every 100ms for smooth animation

    def create_database_display_section(self):
        """Create the database records display section in the main window"""
        # Database records section
        self.db_section_frame = tk.Frame(self.root, highlightbackground="black", highlightthickness=2)
        self.db_section_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Title for database section
        title_frame = tk.Frame(self.db_section_frame, bg="#FF6600")  # Orange header
        title_frame.pack(fill="x")

        tk.Label(title_frame, text="DATABASE RECORDS - GP12 INSPECTION TRACEABILITY",
                font=("Arial", 18, "bold"), bg="#FF6600", fg="white", pady=10).pack()

        # Create scrollable frame for database records
        self.db_canvas = tk.Canvas(self.db_section_frame, height=300)
        self.db_scrollbar = tk.Scrollbar(self.db_section_frame, orient="vertical", command=self.db_canvas.yview)
        self.db_scrollable_frame = tk.Frame(self.db_canvas)

        self.db_scrollable_frame.bind(
            "<Configure>",
            lambda e: self.db_canvas.configure(scrollregion=self.db_canvas.bbox("all"))
        )

        self.db_canvas.create_window((0, 0), window=self.db_scrollable_frame, anchor="nw")
        self.db_canvas.configure(yscrollcommand=self.db_scrollbar.set)

        self.db_canvas.pack(side="left", fill="both", expand=True)
        self.db_scrollbar.pack(side="right", fill="y")

        # Load initial database data
        self.refresh_database_display()

    def refresh_database_display(self):
        """Refresh the database display in the main window"""
        self.load_database_data_main_window()

    def load_database_data_main_window(self):
        """Load database data into the main window display"""
        # Clear existing data
        for widget in self.db_scrollable_frame.winfo_children():
            widget.destroy()

        try:
            # Connect to SQL Server
            conn = pyodbc.connect(
                f'DRIVER={SQL_DRIVER};SERVER={SQL_SERVER};DATABASE={SQL_DATABASE};'
                f'Trusted_Connection=yes;'
            )

            cursor = conn.cursor()

            # First, let's check what columns actually exist in the table
            try:
                # Get column information
                cursor.execute("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'parameter'")
                columns = [row[0] for row in cursor.fetchall()]
                print(f"Available columns: {columns}")
            except Exception as col_error:
                print(f"Error getting column info: {col_error}")

            # Query to get the latest 50 records, ordered by newest first
            # Use the correct column name: [DATE TIME] (with space, not "AND")
            query = """
            SELECT TOP 50 S_NO, [DATE TIME], MODEL_NAME, BARCODE_DATA, BARCODE_SIZE, REMARKES
            FROM dbo.parameter
            ORDER BY [DATE TIME] DESC
            """

            cursor.execute(query)

            # Create header row
            header_frame = tk.Frame(self.db_scrollable_frame, bg="#FF6600")  # Orange header
            header_frame.pack(fill="x", pady=2)

            headers = ["S_NO", "DATE TIME", "BARCODE_DATA", "BARCODE_SIZE", "REMARKS"]
            col_widths = [10, 20, 40, 15, 30]

            for i, (header, width) in enumerate(zip(headers, col_widths)):
                tk.Label(header_frame, text=header, font=("Arial", 16, "bold"),
                        bg="#FF6600", fg="white", width=width, anchor="center", padx=5, pady=8).pack(side="left")

            # Add data rows with alternating colors
            for row_idx, row in enumerate(cursor.fetchall()):
                # Alternate between orange and white
                bg_color = "#FFE0B3" if row_idx % 2 == 0 else "white"  # Light orange and white

                row_frame = tk.Frame(self.db_scrollable_frame, bg=bg_color)
                row_frame.pack(fill="x", pady=1)

                # Extract only the columns we want to display: S_NO, DATE AND TIME, BARCODE_DATA, BARCODE_SIZE, REMARKES
                display_data = [row[0], row[1], row[3], row[4], row[5]]  # Skip MODEL_NAME (row[2])

                for col_idx, (width, value) in enumerate(zip(col_widths, display_data)):
                    display_value = str(value) if value is not None else ""

                    # Format datetime for better readability
                    if col_idx == 1 and value:  # DATE AND TIME column
                        try:
                            display_value = value.strftime("%Y-%m-%d %H:%M:%S")
                        except:
                            pass

                    # Truncate long text for display but keep more characters for better visibility
                    if len(display_value) > width * 1.5:
                        display_value = display_value[:int(width * 1.5) - 3] + "..."

                    # Set alignment based on column type
                    if col_idx == 0:  # S_NO - center alignment
                        anchor_type = "center"
                    elif col_idx == 1:  # DATE AND TIME - center alignment
                        anchor_type = "center"
                    else:  # Other columns - left alignment
                        anchor_type = "w"

                    label = tk.Label(row_frame, text=display_value, font=("Arial", 14, "bold"),
                                    bg=bg_color, width=width, anchor=anchor_type, padx=5, pady=4)
                    label.pack(side="left")

            conn.close()

            # Update status
            self.show_db_status("Database refreshed successfully", "green")

        except Exception as e:
            # Show error message
            error_frame = tk.Frame(self.db_scrollable_frame, bg="#FFE0E0")
            error_frame.pack(fill="x", pady=10)

            error_label = tk.Label(error_frame, text=f"Error loading database: {str(e)}",
                                  font=("Arial", 14, "bold"), fg="red", bg="#FFE0E0", pady=10)
            error_label.pack()

            self.show_db_status(f"Database error: {str(e)}", "red")
            print(f"Database error: {e}")

    def show_database_table(self):
        """Show a window with the database table contents"""
        if self.db_table_window is None or not self.db_table_window.winfo_exists():
            self.db_table_window = tk.Toplevel(self.root)
            self.db_table_window.title("Database Records")
            self.db_table_window.geometry("1200x600")

            # Create a frame for the table
            table_frame = tk.Frame(self.db_table_window)
            table_frame.pack(fill="both", expand=True, padx=10, pady=10)

            # Add a title
            tk.Label(table_frame, text="GP12 Database Records",
                    font=("Arial", 16, "bold")).pack(pady=(0, 10))

            # Create a frame for the table header
            header_frame = tk.Frame(table_frame, bg="#4CAF50")
            header_frame.pack(fill="x")

            # Define column widths
            col_widths = {
                "S_NO": 8,
                "DATE TIME": 20,
                "MODEL_NAME": 15,
                "BARCODE_DATA": 40,
                "BARCODE_SIZE": 12,
                "REMARKES": 30
            }

            # Create header labels
            for i, (col_name, width) in enumerate(col_widths.items()):
                tk.Label(header_frame, text=col_name, font=("Arial", 12, "bold"),
                        bg="#4CAF50", fg="white", width=width, anchor="w", padx=5).grid(row=0, column=i, sticky="ew")

            # Create a canvas for scrolling
            canvas = tk.Canvas(table_frame)
            scrollbar = tk.Scrollbar(table_frame, orient="vertical", command=canvas.yview)
            scrollable_frame = tk.Frame(canvas)

            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            canvas.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")

            # Add refresh button
            refresh_button = tk.Button(self.db_table_window, text="Refresh Data",
                                      command=lambda: self.load_database_data(scrollable_frame, col_widths),
                                      bg="#2196F3", fg="white", font=("Arial", 12))
            refresh_button.pack(pady=10)

            # Load data initially
            self.load_database_data(scrollable_frame, col_widths)
        else:
            self.db_table_window.lift()  # Bring window to front if already exists

    def load_database_data(self, frame, col_widths):
        """Load data from database into the table frame"""
        # Clear existing data
        for widget in frame.winfo_children():
            widget.destroy()

        try:
            # Connect to SQL Server
            conn = pyodbc.connect(
                f'DRIVER={SQL_DRIVER};SERVER={SQL_SERVER};DATABASE={SQL_DATABASE};'
                f'Trusted_Connection=yes;'
            )

            cursor = conn.cursor()

            # Query to get the latest 100 records, ordered by newest first
            # Use correct column name: [DATE TIME] (with space, not "AND")
            query = """
            SELECT TOP 100 S_NO, [DATE TIME], MODEL_NAME, BARCODE_DATA, BARCODE_SIZE, REMARKES
            FROM dbo.parameter
            ORDER BY [DATE TIME] DESC
            """

            cursor.execute(query)

            # Add data rows
            for row_idx, row in enumerate(cursor.fetchall()):
                bg_color = "#f0f0f0" if row_idx % 2 == 0 else "white"  # Alternating row colors

                for col_idx, (col_name, width) in enumerate(col_widths.items()):
                    value = str(row[col_idx]) if row[col_idx] is not None else ""
                    if col_name == "DATE TIME" and value:
                        # Format datetime for better readability
                        try:
                            dt = row[col_idx]
                            value = dt.strftime("%Y-%m-%d %H:%M:%S")
                        except:
                            pass

                    label = tk.Label(frame, text=value, font=("Arial", 10),
                                    bg=bg_color, width=width, anchor="w", padx=5,
                                    wraplength=width*10)  # Allow wrapping for long text
                    label.grid(row=row_idx, column=col_idx, sticky="ew", pady=2)

            conn.close()

        except Exception as e:
            # Show error message in the table
            error_label = tk.Label(frame, text=f"Error loading data: {str(e)}",
                                  font=("Arial", 12), fg="red", pady=20)
            error_label.grid(row=0, column=0, columnspan=len(col_widths))
            print(f"Database error: {e}")

if __name__ == "__main__":
    root = tk.Tk()
    app = PLCReaderApp(root)
    root.mainloop()

